import { Metadata } from 'next'
import { getTranslations, setRequestLocale } from 'next-intl/server'
import { Link } from '@/i18n/navigation'
import { ChevronRight } from 'lucide-react'
import { routing } from '@/i18n/routing'
import VideoTrimmerClient from '@/components/video-trimmer-client'
import { EnhancedToolSchema } from '@/components/schema/enhanced-tool-schema'
import { APP_NAME, SITE_URL } from '@/lib/constants'
import NoSSRWrapper from '@/components/no-ssr-wrapper'
import { MarkdownContent } from '@/components/markdown-content'
import { getToolMarkdown } from '@/lib/markdown'
import { RelatedToolsSidebar } from '@/components/related-tools-sidebar'

export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }))
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params
  const t = await getTranslations({ locale })

  // Try to get metadata from markdown file first
  const markdown = await getToolMarkdown('video-cutter', locale)

  // If markdown exists, use its frontmatter for metadata
  if (markdown && markdown.frontmatter) {
    const { title, description, keywords } = markdown.frontmatter

    return {
      title: title || t('tools.video-cutter.title'),
      description: description || t('tools.video-cutter.description'),
      keywords: keywords,
      alternates: {
        canonical: `/${locale}/tools/video-cutter`,
        languages: Object.fromEntries(
          routing.locales.map((l) => [l, `/${l}/tools/video-cutter`])
        ),
      },
      openGraph: {
        type: 'website',
        title: `${title} | ${APP_NAME}` || `${t('tools.video-cutter.title')} | ${APP_NAME}`,
        description: description || t('tools.video-cutter.description'),
        url: `${SITE_URL}/${locale}/tools/video-cutter`,
        siteName: APP_NAME,
        images: [
          {
            url: `${SITE_URL}/og-image.jpg`,
            width: 1200,
            height: 630,
            alt: title || t('tools.video-cutter.title'),
          },
        ],
      },
      twitter: {
        card: 'summary_large_image',
        title: `${title} | ${APP_NAME}` || `${t('tools.video-cutter.title')} | ${APP_NAME}`,
        description: description || t('tools.video-cutter.description'),
        images: [`${SITE_URL}/og-image.jpg`],
      },
    }
  }

  // Fallback to translation keys if no markdown
  return {
    title: t('tools.video-cutter.title'),
    description: t('tools.video-cutter.description'),
    alternates: {
      canonical: `/${locale}/tools/video-cutter`,
      languages: Object.fromEntries(
        routing.locales.map((l) => [l, `/${l}/tools/video-cutter`])
      ),
    },
    openGraph: {
      type: 'website',
      title: `${t('tools.video-cutter.title')} | ${APP_NAME}`,
      description: t('tools.video-cutter.description'),
      url: `${SITE_URL}/${locale}/tools/video-cutter`,
      siteName: APP_NAME,
      images: [
        {
          url: `${SITE_URL}/og-image.jpg`,
          width: 1200,
          height: 630,
          alt: t('tools.video-cutter.title'),
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: `${t('tools.video-cutter.title')} | ${APP_NAME}`,
      description: t('tools.video-cutter.description'),
      images: [`${SITE_URL}/og-image.jpg`],
    },
  }
}

export default async function VideoCutterPage({
  params,
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params

  // Enable static rendering
  setRequestLocale(locale)

  // Get translations
  const t = await getTranslations({ locale })

  // Get markdown content
  const markdown = await getToolMarkdown('video-cutter', locale)

  return (
    <div className="container mx-auto">
      {/* Breadcrumbs */}
      <nav className="text-muted-foreground mb-6 flex items-center text-sm">
        <Link href={`/`} className="hover:text-foreground">
          {t('nav.home')}
        </Link>
        <ChevronRight className="mx-2 h-4 w-4" />
        <Link href={`/tools`} className="hover:text-foreground">
          {t('nav.tools')}
        </Link>
        <ChevronRight className="mx-2 h-4 w-4" />
        <span className="text-foreground font-medium">
          {markdown?.frontmatter?.title || t('tools.video-cutter.title')}
        </span>
      </nav>

      <div className="grid grid-cols-1 gap-8 md:grid-cols-3 lg:grid-cols-4">
        {/* Main Content */}
        <div className="md:col-span-2 lg:col-span-3">
          <div className="mb-8">
            <h1 className="mb-2 text-3xl font-bold tracking-tight">
              {markdown?.frontmatter?.title || t('tools.video-cutter.title')}
            </h1>
            <p className="text-muted-foreground">
              {markdown?.frontmatter?.description || t('tools.video-cutter.description')}
            </p>
          </div>

          <EnhancedToolSchema
            slug="video-cutter"
            rating={4.8}
            ratingCount={35}
            datePublished="2023-01-01"
            dateModified={new Date().toISOString().split('T')[0]}
          />

          {/* Video Cutter Tool */}
          <div className="mb-12">
            <NoSSRWrapper>
              <VideoTrimmerClient />
            </NoSSRWrapper>
          </div>

          {/* Markdown Content */}
          {markdown?.content && (
            <div className="mt-12 border-t pt-8">
              <h2 className="mb-6 text-2xl font-bold">About This Tool</h2>
              <MarkdownContent content={markdown.content} />
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="md:col-span-1">
          <RelatedToolsSidebar currentToolSlug="video-cutter" maxTools={8} />
        </div>
      </div>
    </div>
  )
}
