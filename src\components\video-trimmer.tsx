'use client'

import React, { useState, useRef, useCallback } from 'react'
import ReactPlayer from 'react-player'
import { FFmpeg } from '@ffmpeg/ffmpeg'
import { fetchFile, toBlobURL } from '@ffmpeg/util'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { VideoTimeline } from '@/components/ui/video-timeline'
import {
  validateVideoFile,
  generateThumbnails,
  getVideoMetadata
} from '@/lib/video-utils'

interface VideoTrimmerProps {
  className?: string
}

export function VideoTrimmer({ className }: VideoTrimmerProps) {
  // File and video state
  const [file, setFile] = useState<File | null>(null)
  const [videoUrl, setVideoUrl] = useState<string | null>(null)
  const [duration, setDuration] = useState<number>(0)
  const [thumbnails, setThumbnails] = useState<string[]>([])

  // Trimming state
  const [trimRange, setTrimRange] = useState<[number, number]>([0, 0])
  const [currentTime, setCurrentTime] = useState<number>(0)

  // Processing state
  const [isLoadingThumbnails, setIsLoadingThumbnails] = useState(false)
  const [isFFmpegLoading, setIsFFmpegLoading] = useState(false)
  const [ffmpegLoaded, setFFmpegLoaded] = useState(false)
  const [isTrimming, setIsTrimming] = useState(false)
  const [progress, setProgress] = useState(0)
  const [outputUrl, setOutputUrl] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)

  // Refs
  const playerRef = useRef<ReactPlayer>(null)
  const ffmpegRef = useRef<FFmpeg | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Initialize FFmpeg ref on client side only
  React.useEffect(() => {
    if (typeof window !== 'undefined' && !ffmpegRef.current) {
      ffmpegRef.current = new FFmpeg()
    }
  }, [])

  // Load FFmpeg
  const loadFFmpeg = useCallback(async () => {
    if (ffmpegLoaded || !ffmpegRef.current) return

    setIsFFmpegLoading(true)
    setError(null)

    try {
      const ffmpeg = ffmpegRef.current

      // ffmpeg.on('log', ({ message }) => {
      //   console.log(message)
      // })

      ffmpeg.on('progress', ({ progress }) => {
        setProgress(Math.min(100, Math.round(progress * 100)))
      })

      // Try multiple CDN sources for better reliability
      const cdnSources = [
        'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd',
        'https://cdn.jsdelivr.net/npm/@ffmpeg/core@0.12.6/dist/umd',
        'https://unpkg.com/@ffmpeg/core-mt@0.12.6/dist/umd'
      ]

      let loaded = false
      for (const baseURL of cdnSources) {
        try {
          await ffmpeg.load({
            coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
            wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
          })
          loaded = true
          break
        } catch (cdnError) {
          console.warn(`Failed to load from ${baseURL}:`, cdnError)
          continue
        }
      }

      if (!loaded) {
        throw new Error('All CDN sources failed')
      }

      setFFmpegLoaded(true)
    } catch (error) {
      console.error('Error loading FFmpeg:', error)
      setError('Failed to load video processing engine. Please check your internet connection and try again.')
    } finally {
      setIsFFmpegLoading(false)
    }
  }, [ffmpegLoaded])

  // Handle file selection
  const handleFileSelect = useCallback(async (selectedFile: File) => {
    setError(null)

    // Validate file
    const validation = validateVideoFile(selectedFile)
    if (!validation.isValid) {
      setError(validation.error || 'Invalid file')
      return
    }

    setFile(selectedFile)
    const url = URL.createObjectURL(selectedFile)
    setVideoUrl(url)

    try {
      // Get video metadata
      const metadata = await getVideoMetadata(selectedFile)
      setDuration(metadata.duration)
      setTrimRange([0, metadata.duration])

      // Generate thumbnails
      setIsLoadingThumbnails(true)
      const thumbs = await generateThumbnails(selectedFile, 10)
      setThumbnails(thumbs)

      // Auto-load FFmpeg in the background after video is loaded
      if (!ffmpegLoaded && !isFFmpegLoading) {
        loadFFmpeg()
      }
    } catch (error) {
      console.error('Error processing video:', error)
      setError('Error processing video file. Please try a different file.')
    } finally {
      setIsLoadingThumbnails(false)
    }
  }, [ffmpegLoaded, isFFmpegLoading, loadFFmpeg])

  // Handle file input change
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0]
    if (selectedFile) {
      handleFileSelect(selectedFile)
    }
  }

  // Handle drag and drop
  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    const droppedFile = event.dataTransfer.files[0]
    if (droppedFile) {
      handleFileSelect(droppedFile)
    }
  }

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
  }

  // Handle video player events
  const handleProgress = (state: { playedSeconds: number }) => {
    setCurrentTime(state.playedSeconds)
  }

  const handleSeek = useCallback((time: number) => {
    if (playerRef.current) {
      playerRef.current.seekTo(time, 'seconds')
      setCurrentTime(time)
    }
  }, [])

  // Handle range change
  const handleRangeChange = useCallback((newRange: [number, number]) => {
    setTrimRange(newRange)
  }, [])

  // Preview selected clip
  const previewClip = () => {
    if (playerRef.current) {
      playerRef.current.seekTo(trimRange[0], 'seconds')
      setCurrentTime(trimRange[0])
    }
  }

  // Trim video
  const trimVideo = async () => {
    if (!file || !ffmpegRef.current) {
      setError('Video processing not available. Please refresh the page.')
      return
    }

    setIsTrimming(true)
    setProgress(0)
    setError(null)
    setOutputUrl(null)

    try {
      // Ensure FFmpeg is loaded before proceeding
      if (!ffmpegLoaded) {
        // If FFmpeg is currently loading, wait for it
        if (isFFmpegLoading) {
          // Wait for FFmpeg to finish loading (with timeout)
          let attempts = 0
          while (!ffmpegLoaded && isFFmpegLoading && attempts < 30) {
            await new Promise(resolve => setTimeout(resolve, 1000))
            attempts++
          }
        } else {
          // Start loading FFmpeg if not already loading
          await loadFFmpeg()
        }

        // Final check after loading
        if (!ffmpegLoaded) {
          setError('Failed to load video processing engine. Please try again.')
          return
        }
      }

      const ffmpeg = ffmpegRef.current
      const inputFileName = `input.${file.name.split('.').pop()}`
      const outputFileName = `trimmed.mp4`

      // Write input file
      await ffmpeg.writeFile(inputFileName, await fetchFile(file))

      // Prepare FFmpeg command for trimming
      const startTime = trimRange[0]
      const duration = trimRange[1] - trimRange[0]

      const command = [
        '-i', inputFileName,
        '-ss', startTime.toString(),
        '-t', duration.toString(),
        '-c', 'copy', // Use stream copy for faster processing
        '-avoid_negative_ts', 'make_zero',
        outputFileName
      ]

      // Execute FFmpeg command
      await ffmpeg.exec(command)

      // Read output file
      const data = await ffmpeg.readFile(outputFileName)

      // Create download URL
      const uint8Data = typeof data === 'string' ? new Uint8Array([]) : new Uint8Array(data as Uint8Array);
      const url = URL.createObjectURL(
        new Blob([uint8Data.buffer], {
          type: 'video/mp4',
        })
      )

      setOutputUrl(url)
    } catch (error) {
      console.error('Error trimming video:', error)
      setError('Failed to trim video. Please try again with a different file or range.')
    } finally {
      setIsTrimming(false)
    }
  }

  // Reset everything
  const resetAll = () => {
    setFile(null)
    setVideoUrl(null)
    setDuration(0)
    setThumbnails([])
    setTrimRange([0, 0])
    setCurrentTime(0)
    setOutputUrl(null)
    setError(null)
    setProgress(0)
  }

  return (
    <div className={className}>
      <div className="space-y-4 sm:space-y-6">
        {/* File Upload */}
        {!file ? (
          <Card>
            <CardContent className="p-4 sm:p-6">
              <div
                className="border-muted rounded-lg border-2 border-dashed p-6 sm:p-8 text-center"
                onDrop={handleDrop}
                onDragOver={handleDragOver}
              >
                <div className="flex flex-col items-center justify-center space-y-4">
                  <div className="bg-primary/10 rounded-full p-4 sm:p-6">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="32"
                      height="32"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-primary w-6 h-6 sm:w-8 sm:h-8"
                    >
                      <path d="m15 2 3 3.3a1 1 0 0 1 .3.7v11.2a1 1 0 0 1-.3.7l-3 3.3"/>
                      <path d="M9 22H4a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1h5"/>
                      <path d="M7 8v4"/>
                      <path d="M5 10h4"/>
                    </svg>
                  </div>
                  <div className="space-y-2">
                    <p className="text-foreground font-medium text-sm sm:text-base">
                      Upload Your Video
                    </p>
                    <p className="text-muted-foreground text-xs sm:text-sm">
                      Drag and drop a video file here, or click to select
                    </p>
                    <p className="text-muted-foreground text-xs">
                      Supports MP4, WebM, OGG, AVI, MOV (max 500MB)
                    </p>
                  </div>
                  <Button
                    size="lg"
                    className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium px-6 py-3 text-sm sm:text-base"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="mr-2"
                    >
                      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                      <polyline points="17 8 12 3 7 8"/>
                      <line x1="12" y1="3" x2="12" y2="15"/>
                    </svg>
                    Select Video File
                  </Button>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="video/*"
                    onChange={handleFileChange}
                    className="hidden"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          <>
            {/* Video Player */}
            <Card>
              <CardContent className="p-4 sm:p-6">
                <div className="aspect-video w-full overflow-hidden rounded-lg bg-black">
                  <ReactPlayer
                    ref={playerRef}
                    url={videoUrl ?? undefined}
                    width="100%"
                    height="100%"
                    controls
                    onProgress={handleProgress}
                    onDuration={setDuration}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Integrated Timeline and Controls */}
            {thumbnails.length > 0 && (
              <Card>
                <CardContent className="p-4 sm:p-6">
                  <div className="space-y-4 sm:space-y-6">
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                      <h3 className="text-lg sm:text-xl font-medium">Video Trimmer</h3>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={resetAll}
                        className="bg-primary/5 border-primary/20 text-primary hover:bg-primary hover:text-primary-foreground transition-colors font-medium self-start sm:self-auto"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="mr-2"
                        >
                          <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"/>
                          <path d="M3 3v5h5"/>
                        </svg>
                        Upload New Video
                      </Button>
                    </div>

                    {isLoadingThumbnails ? (
                      <div className="flex items-center justify-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                        <span className="ml-2 text-sm text-muted-foreground">
                          Generating thumbnails...
                        </span>
                      </div>
                    ) : (
                      <>
                        {/* Timeline */}
                        <VideoTimeline
                          duration={duration}
                          thumbnails={thumbnails}
                          value={trimRange}
                          onChange={handleRangeChange}
                          currentTime={currentTime}
                          onSeek={handleSeek}
                        />

                        {/* Action Buttons Row */}
                        <div className="space-y-4">
                          {/* Preview Button - Full width on mobile */}
                          <Button
                            variant="outline"
                            onClick={previewClip}
                            className="w-full sm:w-auto"
                            size="lg"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="mr-2"
                            >
                              <polygon points="5 3 19 12 5 21 5 3"/>
                            </svg>
                            Preview Selection
                          </Button>

                          {/* Status and Progress Section */}
                          <div className="space-y-3">
                            {/* FFmpeg Status - Only show if there's an error */}
                            {!ffmpegLoaded && !isFFmpegLoading && error?.includes('video processing') && (
                              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                                  <p className="text-yellow-800 text-xs sm:text-sm">Processor failed to load</p>
                                  <Button
                                    onClick={loadFFmpeg}
                                    variant="outline"
                                    size="sm"
                                    disabled={isFFmpegLoading}
                                    className="h-8 px-3 text-xs w-full sm:w-auto"
                                  >
                                    Retry
                                  </Button>
                                </div>
                              </div>
                            )}

                            {/* Background Loading Indicator */}
                            {!ffmpegLoaded && isFFmpegLoading && (
                              <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                                <div className="flex items-center justify-center space-x-2">
                                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                                  <p className="text-blue-800 text-xs sm:text-sm">Preparing processor...</p>
                                </div>
                              </div>
                            )}

                            {/* Progress Bar */}
                            {isTrimming && (
                              <div className="space-y-2">
                                <Progress value={progress} className="h-3" />
                                <p className="text-muted-foreground text-center text-xs sm:text-sm">
                                  Trimming video... {progress}%
                                </p>
                              </div>
                            )}
                          </div>

                          {/* Trim Button - Full width on mobile */}
                          <Button
                            onClick={trimVideo}
                            disabled={isTrimming || trimRange[1] - trimRange[0] <= 0}
                            size="lg"
                            className="w-full font-medium"
                          >
                            {isTrimming ? (
                              <>
                                <span className="mr-2 animate-spin">
                                  <svg
                                    viewBox="0 0 1024 1024"
                                    focusable="false"
                                    data-icon="loading"
                                    width="1em"
                                    height="1em"
                                    fill="currentColor"
                                    aria-hidden="true"
                                  >
                                    <path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path>
                                  </svg>
                                </span>
                                Trimming...
                              </>
                            ) : !ffmpegLoaded && isFFmpegLoading ? (
                              <>
                                <span className="mr-2 animate-spin">
                                  <svg
                                    viewBox="0 0 1024 1024"
                                    focusable="false"
                                    data-icon="loading"
                                    width="1em"
                                    height="1em"
                                    fill="currentColor"
                                    aria-hidden="true"
                                  >
                                    <path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path>
                                  </svg>
                                </span>
                                Preparing...
                              </>
                            ) : !ffmpegLoaded ? (
                              <>
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="16"
                                  height="16"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  className="mr-2"
                                >
                                  <path d="M12 2v10l3-3m-3 3l-3-3"/>
                                  <path d="M12 21a9 9 0 1 1 0-18"/>
                                </svg>
                                Loading...
                              </>
                            ) : (
                              <>
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="16"
                                  height="16"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  className="mr-2"
                                >
                                  <path d="M9 12l2 2 4-4"/>
                                  <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z"/>
                                </svg>
                                Trim Video
                              </>
                            )}
                          </Button>
                        </div>
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Download Section */}
            {outputUrl && (
              <Card>
                <CardContent className="p-4 sm:p-6">
                  <div className="space-y-4 sm:space-y-6 text-center">
                    <div className="bg-green-50 border border-green-200 rounded-md p-4 sm:p-6">
                      <h3 className="text-lg sm:text-xl font-medium text-green-800 mb-2">
                        Video Trimmed Successfully!
                      </h3>
                      <p className="text-green-700 text-sm sm:text-base">
                        Your trimmed video is ready for download.
                      </p>
                    </div>

                    <Button asChild size="lg" className="w-full sm:w-auto">
                      <a
                        href={outputUrl}
                        download={`${file?.name.split('.')[0] || 'video'}_trimmed.mp4`}
                        className="inline-flex items-center justify-center"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="20"
                          height="20"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="mr-2"
                        >
                          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                          <polyline points="7 10 12 15 17 10"/>
                          <line x1="12" y1="15" x2="12" y2="3"/>
                        </svg>
                        Download Trimmed Video
                      </a>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </>
        )}

        {/* Error Display */}
        {error && (
          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="bg-red-50 border border-red-200 rounded-md p-4 sm:p-6">
                <p className="text-red-800 text-sm sm:text-base">{error}</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
