'use client'

import React, { useState, useRef, useCallback } from 'react'
import ReactPlayer from 'react-player'
import { FFmpeg } from '@ffmpeg/ffmpeg'
import { fetchFile, toBlobURL } from '@ffmpeg/util'
import { useTranslations } from 'next-intl'
import {
  Upload,
  Video,
  RotateCcw,
  Play,
  Loader2,
  Download,
  Scissors
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { VideoTimeline } from '@/components/ui/video-timeline'
import {
  validateVideoFile,
  generateThumbnails,
  getVideoMetadata
} from '@/lib/video-utils'

interface VideoTrimmerProps {
  className?: string
}

export function VideoTrimmer({ className }: VideoTrimmerProps) {
  const t = useTranslations('tools.video-trimmer')

  // File and video state
  const [file, setFile] = useState<File | null>(null)
  const [videoUrl, setVideoUrl] = useState<string | null>(null)
  const [duration, setDuration] = useState<number>(0)
  const [thumbnails, setThumbnails] = useState<string[]>([])

  // Trimming state
  const [trimRange, setTrimRange] = useState<[number, number]>([0, 0])
  const [currentTime, setCurrentTime] = useState<number>(0)

  // Processing state
  const [isLoadingThumbnails, setIsLoadingThumbnails] = useState(false)
  const [isFFmpegLoading, setIsFFmpegLoading] = useState(false)
  const [ffmpegLoaded, setFFmpegLoaded] = useState(false)
  const [isTrimming, setIsTrimming] = useState(false)
  const [progress, setProgress] = useState(0)
  const [outputUrl, setOutputUrl] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)

  // Refs
  const playerRef = useRef<ReactPlayer>(null)
  const ffmpegRef = useRef<FFmpeg | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Initialize FFmpeg ref on client side only
  React.useEffect(() => {
    if (typeof window !== 'undefined' && !ffmpegRef.current) {
      ffmpegRef.current = new FFmpeg()
    }
  }, [])

  // Load FFmpeg
  const loadFFmpeg = useCallback(async () => {
    if (ffmpegLoaded || !ffmpegRef.current) return

    setIsFFmpegLoading(true)
    setError(null)

    try {
      const ffmpeg = ffmpegRef.current

      // ffmpeg.on('log', ({ message }) => {
      //   console.log(message)
      // })

      ffmpeg.on('progress', ({ progress }) => {
        setProgress(Math.min(100, Math.round(progress * 100)))
      })

      // Try multiple CDN sources for better reliability
      const cdnSources = [
        'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd',
        'https://cdn.jsdelivr.net/npm/@ffmpeg/core@0.12.6/dist/umd',
        'https://unpkg.com/@ffmpeg/core-mt@0.12.6/dist/umd'
      ]

      let loaded = false
      for (const baseURL of cdnSources) {
        try {
          await ffmpeg.load({
            coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
            wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
          })
          loaded = true
          break
        } catch (cdnError) {
          console.warn(`Failed to load from ${baseURL}:`, cdnError)
          continue
        }
      }

      if (!loaded) {
        throw new Error('All CDN sources failed')
      }

      setFFmpegLoaded(true)
    } catch (error) {
      console.error('Error loading FFmpeg:', error)
      setError('Failed to load video processing engine. Please check your internet connection and try again.')
    } finally {
      setIsFFmpegLoading(false)
    }
  }, [ffmpegLoaded])

  // Handle file selection
  const handleFileSelect = useCallback(async (selectedFile: File) => {
    setError(null)

    // Validate file
    const validation = validateVideoFile(selectedFile)
    if (!validation.isValid) {
      setError(validation.error || 'Invalid file')
      return
    }

    setFile(selectedFile)
    const url = URL.createObjectURL(selectedFile)
    setVideoUrl(url)

    try {
      // Get video metadata
      const metadata = await getVideoMetadata(selectedFile)
      setDuration(metadata.duration)
      setTrimRange([0, metadata.duration])

      // Generate thumbnails
      setIsLoadingThumbnails(true)
      const thumbs = await generateThumbnails(selectedFile, 10)
      setThumbnails(thumbs)

      // Auto-load FFmpeg in the background after video is loaded
      if (!ffmpegLoaded && !isFFmpegLoading) {
        loadFFmpeg()
      }
    } catch (error) {
      console.error('Error processing video:', error)
      setError('Error processing video file. Please try a different file.')
    } finally {
      setIsLoadingThumbnails(false)
    }
  }, [ffmpegLoaded, isFFmpegLoading, loadFFmpeg])

  // Handle file input change
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0]
    if (selectedFile) {
      handleFileSelect(selectedFile)
    }
  }

  // Handle drag and drop
  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    const droppedFile = event.dataTransfer.files[0]
    if (droppedFile) {
      handleFileSelect(droppedFile)
    }
  }

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
  }

  // Handle video player events
  const handleProgress = (state: { playedSeconds: number }) => {
    setCurrentTime(state.playedSeconds)
  }

  const handleSeek = useCallback((time: number) => {
    if (playerRef.current) {
      playerRef.current.seekTo(time, 'seconds')
      setCurrentTime(time)
    }
  }, [])

  // Handle range change
  const handleRangeChange = useCallback((newRange: [number, number]) => {
    setTrimRange(newRange)
  }, [])

  // Preview selected clip
  const previewClip = () => {
    if (playerRef.current) {
      playerRef.current.seekTo(trimRange[0], 'seconds')
      setCurrentTime(trimRange[0])
    }
  }

  // Trim video
  const trimVideo = async () => {
    if (!file || !ffmpegRef.current) {
      setError('Video processing not available. Please refresh the page.')
      return
    }

    setIsTrimming(true)
    setProgress(0)
    setError(null)
    setOutputUrl(null)

    try {
      // Ensure FFmpeg is loaded before proceeding
      if (!ffmpegLoaded) {
        // If FFmpeg is currently loading, wait for it
        if (isFFmpegLoading) {
          // Wait for FFmpeg to finish loading (with timeout)
          let attempts = 0
          while (!ffmpegLoaded && isFFmpegLoading && attempts < 30) {
            await new Promise(resolve => setTimeout(resolve, 1000))
            attempts++
          }
        } else {
          // Start loading FFmpeg if not already loading
          await loadFFmpeg()
        }

        // Final check after loading
        if (!ffmpegLoaded) {
          setError('Failed to load video processing engine. Please try again.')
          return
        }
      }

      const ffmpeg = ffmpegRef.current
      const inputFileName = `input.${file.name.split('.').pop()}`
      const outputFileName = `trimmed.mp4`

      // Write input file
      await ffmpeg.writeFile(inputFileName, await fetchFile(file))

      // Prepare FFmpeg command for trimming
      const startTime = trimRange[0]
      const duration = trimRange[1] - trimRange[0]

      const command = [
        '-i', inputFileName,
        '-ss', startTime.toString(),
        '-t', duration.toString(),
        '-c', 'copy', // Use stream copy for faster processing
        '-avoid_negative_ts', 'make_zero',
        outputFileName
      ]

      // Execute FFmpeg command
      await ffmpeg.exec(command)

      // Read output file
      const data = await ffmpeg.readFile(outputFileName)

      // Create download URL
      const uint8Data = typeof data === 'string' ? new Uint8Array([]) : new Uint8Array(data as Uint8Array);
      const url = URL.createObjectURL(
        new Blob([uint8Data.buffer], {
          type: 'video/mp4',
        })
      )

      setOutputUrl(url)
    } catch (error) {
      console.error('Error trimming video:', error)
      setError('Failed to trim video. Please try again with a different file or range.')
    } finally {
      setIsTrimming(false)
    }
  }

  // Reset everything
  const resetAll = () => {
    setFile(null)
    setVideoUrl(null)
    setDuration(0)
    setThumbnails([])
    setTrimRange([0, 0])
    setCurrentTime(0)
    setOutputUrl(null)
    setError(null)
    setProgress(0)
  }

  return (
    <div className={className}>
      <div className="space-y-4 sm:space-y-6">
        {/* File Upload */}
        {!file ? (
          <Card>
            <CardContent className="p-4 sm:p-6">
              <div
                className="border-muted rounded-lg border-2 border-dashed p-6 sm:p-8 text-center"
                onDrop={handleDrop}
                onDragOver={handleDragOver}
              >
                <div className="flex flex-col items-center justify-center space-y-4">
                  <div className="bg-primary/10 rounded-full p-4 sm:p-6">
                    <Video className="text-primary w-6 h-6 sm:w-8 sm:h-8" />
                  </div>
                  <div className="space-y-2">
                    <p className="text-foreground font-medium text-sm sm:text-base">
                      {t('uploadYourVideo')}
                    </p>
                    <p className="text-muted-foreground text-xs sm:text-sm">
                      {t('dragAndDrop')}
                    </p>
                    <p className="text-muted-foreground text-xs">
                      {t('supportedFormats')}
                    </p>
                  </div>
                  <Button
                    size="lg"
                    className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium px-6 py-3 text-sm sm:text-base"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Upload className="mr-2 h-5 w-5" />
                    {t('selectVideoFile')}
                  </Button>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="video/*"
                    onChange={handleFileChange}
                    className="hidden"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          <>
            {/* Video Player */}
            <Card>
              <CardContent className="p-4 sm:p-6">
                <div className="aspect-video w-full overflow-hidden rounded-lg bg-black">
                  <ReactPlayer
                    ref={playerRef}
                    url={videoUrl ?? undefined}
                    width="100%"
                    height="100%"
                    controls
                    onProgress={handleProgress}
                    onDuration={setDuration}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Integrated Timeline and Controls */}
            {thumbnails.length > 0 && (
              <Card>
                <CardContent className="p-4 sm:p-6">
                  <div className="space-y-4 sm:space-y-6">
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                      <h3 className="text-lg sm:text-xl font-medium">{t('videoTrimmer')}</h3>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={resetAll}
                        className="bg-primary/5 border-primary/20 text-primary hover:bg-primary hover:text-primary-foreground transition-colors font-medium self-start sm:self-auto"
                      >
                        <RotateCcw className="mr-2 h-4 w-4" />
                        {t('uploadNewVideo')}
                      </Button>
                    </div>

                    {isLoadingThumbnails ? (
                      <div className="flex items-center justify-center py-8">
                        <Loader2 className="h-8 w-8 animate-spin text-primary" />
                        <span className="ml-2 text-sm text-muted-foreground">
                          {t('generatingThumbnails')}
                        </span>
                      </div>
                    ) : (
                      <>
                        {/* Timeline */}
                        <VideoTimeline
                          duration={duration}
                          thumbnails={thumbnails}
                          value={trimRange}
                          onChange={handleRangeChange}
                          currentTime={currentTime}
                          onSeek={handleSeek}
                        />

                        {/* Action Buttons Row */}
                        <div className="space-y-4">
                          {/* Preview Button - Full width on mobile */}
                          <Button
                            variant="outline"
                            onClick={previewClip}
                            className="w-full sm:w-auto"
                            size="lg"
                          >
                            <Play className="mr-2 h-4 w-4" />
                            {t('previewSelection')}
                          </Button>

                          {/* Status and Progress Section */}
                          <div className="space-y-3">
                            {/* FFmpeg Status - Only show if there's an error */}
                            {!ffmpegLoaded && !isFFmpegLoading && error?.includes('video processing') && (
                              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                                  <p className="text-yellow-800 text-xs sm:text-sm">{t('processorFailedToLoad')}</p>
                                  <Button
                                    onClick={loadFFmpeg}
                                    variant="outline"
                                    size="sm"
                                    disabled={isFFmpegLoading}
                                    className="h-8 px-3 text-xs w-full sm:w-auto"
                                  >
                                    {t('retry')}
                                  </Button>
                                </div>
                              </div>
                            )}

                            {/* Background Loading Indicator */}
                            {!ffmpegLoaded && isFFmpegLoading && (
                              <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                                <div className="flex items-center justify-center space-x-2">
                                  <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
                                  <p className="text-blue-800 text-xs sm:text-sm">{t('preparingProcessor')}</p>
                                </div>
                              </div>
                            )}

                            {/* Progress Bar */}
                            {isTrimming && (
                              <div className="space-y-2">
                                <Progress value={progress} className="h-3" />
                                <p className="text-muted-foreground text-center text-xs sm:text-sm">
                                  {t('trimmingVideo', { progress: progress.toString() })}
                                </p>
                              </div>
                            )}
                          </div>

                          {/* Trim Button - Full width on mobile */}
                          <Button
                            onClick={trimVideo}
                            disabled={isTrimming || trimRange[1] - trimRange[0] <= 0}
                            size="lg"
                            className="w-full font-medium"
                          >
                            {isTrimming ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                {t('trimming')}
                              </>
                            ) : !ffmpegLoaded && isFFmpegLoading ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                {t('preparing')}
                              </>
                            ) : !ffmpegLoaded ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4" />
                                {t('loading')}
                              </>
                            ) : (
                              <>
                                <Scissors className="mr-2 h-4 w-4" />
                                {t('trimVideo')}
                              </>
                            )}
                          </Button>
                        </div>
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Download Section */}
            {outputUrl && (
              <Card>
                <CardContent className="p-4 sm:p-6">
                  <div className="space-y-4 sm:space-y-6 text-center">
                    <div className="bg-green-50 border border-green-200 rounded-md p-4 sm:p-6">
                      <h3 className="text-lg sm:text-xl font-medium text-green-800 mb-2">
                        {t('videoTrimmedSuccessfully')}
                      </h3>
                      <p className="text-green-700 text-sm sm:text-base">
                        {t('trimmedVideoReady')}
                      </p>
                    </div>

                    <Button asChild size="lg" className="w-full sm:w-auto">
                      <a
                        href={outputUrl}
                        download={`${file?.name.split('.')[0] || 'video'}_trimmed.mp4`}
                        className="inline-flex items-center justify-center"
                      >
                        <Download className="mr-2 h-5 w-5" />
                        {t('downloadTrimmedVideo')}
                      </a>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </>
        )}

        {/* Error Display */}
        {error && (
          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="bg-red-50 border border-red-200 rounded-md p-4 sm:p-6">
                <p className="text-red-800 text-sm sm:text-base">{error}</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
