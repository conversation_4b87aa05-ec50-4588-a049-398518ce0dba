{"app": {"name": "VDO Tools", "description": "高速で簡単な動画と音声の変換ツール", "tagline": "簡単な数秒のメディア変換", "hero": {"title": {"part1": "あらゆるメディアを", "part2": "数秒で変換"}, "description": "あらゆるメディアニーズに対応する高速、無料、安全なファイル変換ツール。ダウンロード不要。", "cta": "ツールを探索"}, "features": {"easy": "使いやすい", "fast": "超高速", "secure": "100%安全", "free": "完全無料"}, "animation": {"original": "オリジナル", "converting": "変換中", "converted": "変換済み"}}, "nav": {"home": "ホーム", "tools": "ツール", "about": "概要", "features": "機能", "mainFeatures": "主な機能", "advancedFeatures": "高度な機能", "upcomingFeatures": "今後の機能", "feature1": "機能1", "feature2": "機能2", "feature3": "機能3", "feature4": "機能4", "feature5": "機能5", "feature6": "機能6"}, "tools": {"categories": {"video-to-audio": "動画から音声へ", "video-format-converters": "動画フォーマット変換", "audio-format-converters": "音声フォーマット変換", "recording-tools": "録画ツール"}, "universal-format-converter": {"title": "ユニバーサルフォーマットコンバーター", "description": "入力フォーマットを自動検出し、任意のフォーマットに変換", "instructions": "動画ファイルをアップロードすると、自動的にフォーマットを検出します。その後、希望の出力フォーマットを選択して変換します。"}, "common": {"upload": "ファイルをアップロード", "convert": "変換", "download": "ダウンロード", "processing": "処理中...", "dropzone": "ここにファイルをドラッグ＆ドロップするか、クリックして選択", "rate": "このツールを評価", "thanks": "評価ありがとうございます！", "instructions": "手順", "uploadFirst": "ファイルをアップロードして開始", "conversionSuccess": "変換が正常に完了しました！", "conversionFailed": "変換に失敗しました。もう一度お試しください。", "ffmpegLoading": "デバイスを準備中...", "changeFile": "ファイルを変更"}, "mp4-to-mp3": {"title": "MP4からMP3コンバーター", "description": "MP4動画をMP3音声ファイルに変換", "selectFile": "MP4ファイルを選択", "instructions": "MP4動画ファイルをアップロードし、変換をクリックして、MP3音声ファイルをダウンロードします。"}, "video-to-audio": {"title": "動画から音声コンバーター", "description": "あらゆる動画フォーマットから音声を抽出", "selectFile": "動画ファイルを選択", "instructions": "任意の動画ファイルをアップロードし、変換をクリックして、抽出された音声をダウンロードします。"}, "mp4-to-webm": {"title": "MP4からWebMコンバーター", "description": "MP4動画をWebMフォーマットに変換", "selectFile": "MP4ファイルを選択", "instructions": "MP4動画ファイルをアップロードし、変換をクリックして、WebM動画をダウンロードします。"}, "webm-to-mp4": {"title": "WebMからMP4コンバーター", "description": "WebM動画をMP4フォーマットに変換", "selectFile": "WebMファイルを選択", "instructions": "WebM動画ファイルをアップロードし、変換をクリックして、MP4動画をダウンロードします。"}, "mp3-to-wav": {"title": "MP3からWAVコンバーター", "description": "MP3音声をWAVフォーマットに変換", "selectFile": "MP3ファイルを選択", "instructions": "MP3音声ファイルをアップロードし、変換をクリックして、WAV音声をダウンロードします。"}, "wav-to-mp3": {"title": "WAVからMP3コンバーター", "description": "WAV音声をMP3フォーマットに変換", "selectFile": "WAVファイルを選択", "instructions": "WAV音声ファイルをアップロードし、変換をクリックして、MP3音声をダウンロードします。"}, "mov-to-mp4": {"title": "MOVからMP4コンバーター", "description": "MOV動画をMP4フォーマットに変換", "selectFile": "MOVファイルを選択", "instructions": "MOV動画ファイルをアップロードし、変換をクリックして、MP4動画をダウンロードします。"}, "avi-to-mp4": {"title": "AVIからMP4コンバーター", "description": "AVI動画をMP4フォーマットに変換", "selectFile": "AVIファイルを選択", "instructions": "AVI動画ファイルをアップロードし、変換をクリックして、MP4動画をダウンロードします。"}, "mp4-to-gif": {"title": "MP4からGIFコンバーター", "description": "MP4動画を最適化されたアニメーションGIF画像に変換", "selectFile": "MP4ファイルを選択", "instructions": "MP4動画ファイルをアップロードし、変換をクリックして、アニメーションGIFをダウンロードします。コンバーターは良好な品質を維持しながらファイルサイズを最適化します。"}, "gif-to-mp4": {"title": "GIFからMP4コンバーター", "description": "アニメーションGIF画像をMP4動画に変換", "selectFile": "GIFファイルを選択", "instructions": "アニメーションGIFファイルをアップロードし、変換をクリックして、MP4動画をダウンロードします。MP4に変換することで、アニメーションの品質を維持しながらファイルサイズを大幅に削減できます。"}, "screen-recorder": {"title": "画面レコーダー", "description": "画面を録画して動画として保存", "selectFile": "録画開始", "instructions": "録画開始ボタンをクリックし、録画したい画面またはウィンドウを選択して、キャプチャを開始します。終了したら、停止をクリックしてWebMまたはMP4として録画をダウンロードします。", "startRecording": "録画開始", "stopRecording": "録画停止", "pauseRecording": "録画一時停止", "resumeRecording": "録画再開", "downloadWebM": "WebMとしてダウンロード", "downloadMP4": "MP4としてダウンロード", "convertToMP4": "MP4に変換", "selectScreen": "録画対象を選択", "entireScreen": "全画面", "currentWindow": "現在のウィンドウ", "currentTab": "現在のタブ", "withAudio": "音声を含む", "withoutAudio": "音声なし", "recordingTime": "録画時間", "preparing": "録画準備中...", "processing": "録画処理中...", "recordingReady": "録画の準備ができました！", "recordingFailed": "録画に失敗しました。もう一度お試しください。", "conversionFailed": "MP4変換に失敗しました。WebMとしてダウンロードすることはできます。", "noPermission": "画面録画の許可が拒否されました。アクセスを許可して再試行してください。", "browserNotSupported": "お使いのブラウザは画面録画をサポートしていません。Chrome、Edge、またはFirefoxをお試しください。"}, "video-cutter": {"title": "動画カッター", "description": "使いやすい動画トリマーツールを使用して、動画クリップを精密にトリミングおよびカットします", "selectFile": "動画ファイルを選択", "instructions": "動画ファイルをアップロードし、タイムラインを使用して保持したい範囲を選択し、トリミングされた動画をダウンロードします。"}, "video-cropper": {"title": "動画クロッパー", "description": "精密にオンラインで動画をクロップおよびリサイズします。不要な領域を削除し、ソーシャルメディア、プレゼンテーション、またはあらゆる目的のためにアスペクト比を調整します", "selectFile": "動画ファイルを選択", "instructions": "動画ファイルをアップロードし、ビジュアルエディターを使用してクロップエリアを選択し、アスペクト比を選択し、クロップされた動画をダウンロードします。"}, "video-trimmer": {"title": "動画トリマー", "description": "高度なタイムラインエディターを使用して動画クリップを精密にトリミングします。不要なセクションをカットし、必要な部分のみを保持します", "selectFile": "動画ファイルを選択", "instructions": "動画ファイルをアップロードし、タイムラインを使用して開始点と終了点を選択し、トリミングされた動画クリップをダウンロードします。"}}, "seo": {"title": "{toolName} | {appName}", "description": "{toolDescription} - 無料オンラインツール、インストール不要。"}, "footer": {"copyright": "© {year} {appName}. All rights reserved.", "home": "ホーム", "mp4ToMp3": "MP4からMP3", "videoToAudio": "動画から音声", "languages": "言語", "about": "会社概要", "contact": "お問い合わせ", "privacy": "プライバシーポリシー", "terms": "利用規約", "dmca": "DMCA方針"}, "error": {"notFound": {"title": "404 - ページが見つかりません", "heading": "ページが見つかりません", "description": "お探しのページは存在しないか、移動されました。", "goHome": "ホームに戻る"}}, "ui": {"toggleTheme": "テーマ切替", "toggleMenu": "メニュー切替", "language": "言語", "search": "検索"}, "sidebar": {"relatedTools": "関連ツール", "searchTools": "ツールを検索...", "noToolsFound": "ツールが見つかりません", "allTools": "すべてのツール"}, "search": {"placeholder": "ツールを検索...", "noResults": "ツールが見つかりません", "popularTools": "人気のツール", "clear": "検索をクリア"}, "categories": {"videoToAudioTools": "動画から音声へのツール", "videoTools": "動画ツール", "audioTools": "音声ツール", "ourServices": "サービス"}, "formatGroups": {"videoFormats": "動画フォーマット", "audioFormats": "音声フォーマット"}, "tools_list": {"universalFormatConverter": "ユニバーサルフォーマットコンバーター", "mp4ToMp3": "MP4からMP3", "videoToAudio": "動画から音声", "mp4ToWebm": "MP4からWebM", "webmToMp4": "WebMからMP4", "movToMp4": "MOVからMP4", "aviToMp4": "AVIからMP4", "mp4ToGif": "MP4からGIF", "gifToMp4": "GIFからMP4", "mp3ToWav": "MP3からWAV", "wavToMp3": "WAVからMP3", "videoConverter": "動画コンバーター", "videoCompressor": "動画圧縮", "videoEditor": "動画編集", "videoCutter": "動画カッター", "videoCropper": "動画クロッパー", "videoTrimmer": "動画トリマー", "audioConverter": "音声コンバーター", "audioExtractor": "音声抽出", "screenRecorder": "画面レコーダー"}, "dynamicConverter": {"title": "{inputFormat}から{outputFormat}コンバーター", "description": "{inputFormat}ファイルを{outputFormat}フォーマットに変換", "singleFormatTitle": "{format}コンバーター", "singleFormatDescription": "{format}ファイルを他のフォーマットに変換", "selectFile": "ファイルを選択", "inputFormatLabel": "入力フォーマット", "outputFormatLabel": "出力フォーマット", "selectInputFormat": "入力フォーマットを選択", "selectOutputFormat": "出力フォーマットを選択", "settings": "変換設定", "advancedSettings": "詳細設定", "showAdvancedSettings": "詳細設定を表示", "hideAdvancedSettings": "詳細設定を非表示", "resolution": "解像度", "quality": "品質", "fps": "フレームレート", "audioBitrate": "音声ビットレート", "original": "オリジナル", "customSettings": "カスタム設定", "applySettings": "設定を適用", "resetSettings": "デフォルトにリセット", "videoSettings": "動画設定", "audioSettings": "音声設定", "keepOriginalSettings": "オリジナル設定を維持", "showSettings": "設定を表示", "hideSettings": "設定を非表示", "expectedFormat": "予想されるフォーマット: {format}", "customResolution": "カスタム解像度", "width": "幅", "height": "高さ", "aspectRatio": "アスペクト比", "scalingMethod": "スケーリング方法", "keyframeInterval": "キーフレーム間隔", "videoCodec": "動画コーデック", "audioCodec": "音声コーデック", "audioChannels": "音声チャンネル", "audioSampleRate": "サンプルレート", "audioVolume": "音量", "useCustomResolution": "カスタム解像度を使用", "advancedVideoSettings": "詳細な動画設定", "advancedAudioSettings": "詳細な音声設定"}, "pages": {"about": {"title": "会社概要", "description": "VDO Toolsについて、私たちのミッション、そして動画と音声の変換サービスについて詳しく知る。"}, "contact": {"title": "お問い合わせ", "description": "質問、フィードバック、または動画と音声の変換ツールに関するサポートについて、VDO Toolsチームにお問い合わせください。"}, "privacy": {"title": "プライバシーポリシー", "description": "VDO Toolsのプライバシーポリシーは、動画と音声の変換ツールを使用する際のデータの取り扱い方法を説明しています。"}, "terms": {"title": "利用規約", "description": "VDO Toolsの動画と音声の変換サービスを利用するための利用規約。"}, "dmca": {"title": "DMCA方針", "description": "VDO ToolsのDMCA方針と著作権侵害報告手順。"}}, "converters": {"universalFormatConverter": {"title": "ユニバーサルフォーマットコンバーター", "description": "入力フォーマットを自動検出し、任意のフォーマットに変換", "selectFile": "動画ファイルを選択", "instructions": "動画ファイルをアップロードすると、自動的にフォーマットを検出します。その後、希望の出力フォーマットを選択して変換します。", "detectedFormat": "検出されたフォーマット", "selectOutputFormat": "出力フォーマットを選択", "outputFormatLabel": "出力フォーマット"}, "mp4ToMp3": {"title": "MP4からMP3コンバーター", "description": "MP4動画をMP3音声ファイルに変換", "selectFile": "MP4ファイルを選択", "instructions": "MP4動画ファイルをアップロードし、変換をクリックして、MP3音声ファイルをダウンロードします。"}, "videoToAudio": {"title": "動画から音声コンバーター", "description": "あらゆる動画フォーマットから音声を抽出", "selectFile": "動画ファイルを選択", "instructions": "任意の動画ファイルをアップロードし、変換をクリックして、抽出された音声をダウンロードします。"}, "mp4ToWebm": {"title": "MP4からWebMコンバーター", "description": "MP4動画をWebMフォーマットに変換", "selectFile": "MP4ファイルを選択", "instructions": "MP4動画ファイルをアップロードし、変換をクリックして、WebM動画をダウンロードします。"}, "webmToMp4": {"title": "WebMからMP4コンバーター", "description": "WebM動画をMP4フォーマットに変換", "selectFile": "WebMファイルを選択", "instructions": "WebM動画ファイルをアップロードし、変換をクリックして、MP4動画をダウンロードします。"}, "mp3ToWav": {"title": "MP3からWAVコンバーター", "description": "MP3音声をWAVフォーマットに変換", "selectFile": "MP3ファイルを選択", "instructions": "MP3音声ファイルをアップロードし、変換をクリックして、WAV音声をダウンロードします。"}, "wavToMp3": {"title": "WAVからMP3コンバーター", "description": "WAV音声をMP3フォーマットに変換", "selectFile": "WAVファイルを選択", "instructions": "WAV音声ファイルをアップロードし、変換をクリックして、MP3音声をダウンロードします。"}, "movToMp4": {"title": "MOVからMP4コンバーター", "description": "MOV動画をMP4フォーマットに変換", "selectFile": "MOVファイルを選択", "instructions": "MOV動画ファイルをアップロードし、変換をクリックして、MP4動画をダウンロードします。"}, "aviToMp4": {"title": "AVIからMP4コンバーター", "description": "AVI動画をMP4フォーマットに変換", "selectFile": "AVIファイルを選択", "instructions": "AVI動画ファイルをアップロードし、変換をクリックして、MP4動画をダウンロードします。"}, "mp4ToGif": {"title": "MP4からGIFコンバーター", "description": "MP4動画をアニメーションGIF画像に変換", "selectFile": "MP4ファイルを選択", "instructions": "MP4動画ファイルをアップロードし、変換をクリックして、アニメーションGIFをダウンロードします。注：GIFは解像度とフレームレートを削減して、サイズと品質を最適化しています。"}, "gifToMp4": {"title": "GIFからMP4コンバーター", "description": "アニメーションGIF画像をMP4動画に変換", "selectFile": "GIFファイルを選択", "instructions": "アニメーションGIFファイルをアップロードし、変換をクリックして、MP4動画をダウンロードします。MP4に変換することで、アニメーションの品質を維持しながらファイルサイズを大幅に削減できます。"}}}