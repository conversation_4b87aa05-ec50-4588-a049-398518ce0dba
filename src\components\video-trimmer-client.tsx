'use client'

import dynamic from 'next/dynamic'
import { Loader2 } from 'lucide-react'

const VideoTrimmer = dynamic(() => import('./video-trimmer').then(mod => ({ default: mod.VideoTrimmer })), {
  ssr: false,
  loading: () => (
    <div className="flex h-[400px] items-center justify-center">
      <Loader2 className="h-8 w-8 animate-spin text-primary" />
      <span className="ml-2">Loading video trimmer...</span>
    </div>
  ),
})

export default function VideoTrimmerClient() {
  return <VideoTrimmer />
}
